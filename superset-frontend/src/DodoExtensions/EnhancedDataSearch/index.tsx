import { ErrorLevel, styled, SupersetClient, t } from '@superset-ui/core';
import { useState } from 'react';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import Modal from 'src/components/Modal';
import Loading from 'src/components/Loading';
import ErrorAlert from 'src/components/ErrorMessage/ErrorAlert';
import { Link, useLocation } from 'react-router-dom';

interface Response {
  predictions: {
    dashboards: {
      title: string;
      score: number;
      url: string;
    }[];
    meta: {
      query: string;
      retrieved: number;
      top_k: number;
    };
  };
}

const WHITE_LIST_PAGES = [
  '/superset/welcome/',
  '/dashboard/list/',
  '/chart/list/',
];

const ErrorAlertWrapper = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
`;

const TriggerButton = styled.button`
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: none;
  box-shadow: 0 2px 8px ${({ theme }) => theme.colors.grayscale.dark2}1F;
  color: ${({ theme }) => theme.colors.grayscale.base};
  cursor: pointer;
`;

const SearchLoader = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
`;

const ResultsContainer = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
  max-height: 400px;
  overflow-y: auto;
`;

const ResultsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.gridUnit * 3}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 2}px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
`;

const ResultsCount = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};
  font-weight: ${({ theme }) => theme.typography.weights.medium};
`;

const ResultsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
`;

const ResultItem = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.gridUnit * 3}px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.borderRadius}px;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.base};
    box-shadow: 0 2px 8px ${({ theme }) => theme.colors.grayscale.dark2}1A;
    transform: translateY(-1px);
    text-decoration: unset;
  }
`;

const ResultContent = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: ${({ theme }) => theme.gridUnit}px;
`;

const ResultTitle = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  color: ${({ theme }) => theme.colors.primary.dark1};
  line-height: 1.4;
`;

const ResultMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};
`;

const ScoreContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${({ theme }) => theme.gridUnit}px;
`;

const ScoreValue = styled.div<{ value: number }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit}px;
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  color: ${({ theme, value }) =>
    value >= 0.5 ? theme.colors.success.dark1 : theme.colors.error.dark1};
`;

const ScoreBar = styled.div<{ score: number }>`
  width: 60px;
  height: 6px;
  background: ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 3px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${({ score }) => Math.min(score * 100, 100)}%;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors.error.base} 0%,
      ${({ theme }) => theme.colors.warning.base} 50%,
      ${({ theme }) => theme.colors.success.base} 100%
    );
    border-radius: 3px;
    transition: width 0.3s ease;
  }
`;

const ScoreLabel = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.xs}px;
  color: ${({ theme }) => theme.colors.grayscale.light1};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const Legend = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  margin-bottom: ${({ theme }) => theme.gridUnit * 3}px;
  padding: ${({ theme }) => theme.gridUnit * 2}px;
  background: ${({ theme }) => theme.colors.grayscale.light4};
  border-radius: ${({ theme }) => theme.borderRadius}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit}px;
  color: ${({ theme }) => theme.colors.grayscale.dark1};
`;

const LegendScoreIcon = styled.div`
  width: 12px;
  height: 4px;
  border-radius: 2px;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.error.base} 0%,
    ${({ theme }) => theme.colors.warning.base} 50%,
    ${({ theme }) => theme.colors.success.base} 100%
  );
`;

const EnhancedDataSearch = () => {
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<Response | null>(null);
  const [error, setError] = useState<{
    level: ErrorLevel;
    subtitle: string;
    body: string;
  } | null>(null);

  const { pathname } = useLocation();
  const isInWhiteListPage = WHITE_LIST_PAGES.some(page =>
    pathname.includes(page),
  );

  if (!isInWhiteListPage) {
    return null;
  }

  const handleSearch = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);
    SupersetClient.post({
      endpoint: '/api/v1/search',
      jsonPayload: {
        query: searchQuery,
        top_k: 3,
      },
    })
      .then(response => {
        setResponse(response.json as Response);
      })
      .catch(err => {
        const subtitle =
          err.status >= 500
            ? t('Service temporarily unavailable.')
            : t('Unexpected error. Status: %s', err.status);

        setError({
          level: err.status >= 500 ? 'error' : 'warning',
          subtitle,
          body: err.statusText,
        });
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <TriggerButton type="button" onClick={() => setShowModal(true)}>
        <Icons.SearchOutlined iconSize="l" />
      </TriggerButton>
      <Modal
        show={showModal}
        title={t('Enhanced Data Search')}
        onHide={() => setShowModal(false)}
        onHandledPrimaryAction={handleSearch}
        primaryButtonName={t('Search')}
        disablePrimaryButton={!searchQuery || searchQuery.length < 3 || loading}
      >
        <p>
          {t(
            'This is an AI-powered search tool that helps you easily find any necessary dashboard.',
          )}
        </p>
        <Input
          placeholder={t('Search')}
          onChange={e => setSearchQuery(e.target.value)}
        />
        {loading && (
          <SearchLoader>
            <Loading position="inline-centered" />
            <p>{t('Loading...')}</p>
          </SearchLoader>
        )}
        {error && (
          <ErrorAlertWrapper>
            <ErrorAlert
              title={t('Unexpected error')}
              subtitle={error.subtitle}
              level={error.level}
              body={error.body}
            />
          </ErrorAlertWrapper>
        )}
        {!loading && response && (
          <ResultsContainer>
            <ResultsHeader>
              <ResultsCount>
                {t(
                  'Found %s dashboard(s)',
                  response.predictions.dashboards.length,
                )}
              </ResultsCount>
            </ResultsHeader>

            <Legend>
              <LegendItem>
                <LegendScoreIcon />
                <span>{t('Score: Relevance (0.0 - 1.0)')}</span>
              </LegendItem>
            </Legend>

            <ResultsList>
              {response.predictions.dashboards.map(dashboard => (
                <ResultItem
                  key={dashboard.url}
                  to={dashboard.url}
                  target="_blank"
                >
                  <ResultContent>
                    <ResultTitle>{dashboard.title}</ResultTitle>
                    <ResultMeta>
                      <span>{t('Dashboard')}</span>
                      <span>•</span>
                      <span>{t('Click to open')}</span>
                    </ResultMeta>
                  </ResultContent>

                  <ScoreContainer>
                    <ScoreValue value={dashboard.score}>
                      {(dashboard.score * 100).toFixed(0)}%
                    </ScoreValue>
                    <ScoreBar score={dashboard.score} />
                    <ScoreLabel>{t('Relevance')}</ScoreLabel>
                  </ScoreContainer>
                </ResultItem>
              ))}
            </ResultsList>
          </ResultsContainer>
        )}
      </Modal>
    </>
  );
};

export default EnhancedDataSearch;
